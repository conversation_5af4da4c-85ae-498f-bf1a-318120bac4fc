import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'mainLayout', // Ana layout rotası için isim
    component: () => import('layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        name: 'index', // Ana sayfa rotası için isim
        component: () => import('pages/IndexPage.vue'),
        meta: {
          title: 'routes.home', // i18n anahtarı olarak kullanılacak
          showInNav: true, // Navigasyon menüsünde gösterilecek
          icon: 'home', // Ana sayfa için ikon
          order: 1, // Navigasyon sıralaması
        },
      },
      {
        path: '/notifications',
        name: 'notifications',
        component: () => import('pages/NotificationTestPage.vue'),
        meta: {
          title: 'routes.notifications', // i18n anahtarı olarak kullanılacak
          showInNav: true, // Navigasyon menüsünde gösterilecek
          icon: 'notifications', // Bildirim sayfası için ikon
          order: 2, // Navigasyon sıralaması
        },
      },
      {
        path: '/logger-test',
        name: 'loggerTest',
        component: () => import('pages/LoggerTestPage.vue'),
        meta: {
          title: 'routes.loggerTest', // i18n anahtarı olarak kullanılacak
          showInNav: true, // Navigasyon menüsünde gösterilecek
          icon: 'bug_report', // Logger test sayfası için ikon
          order: 3, // Navigasyon sıralaması
        },
      },
    ],
  },

  // Her zaman en sonda bırakılmalı,
  // ancak kaldırılabilir de
  {
    path: '/:catchAll(.*)*',
    name: 'notFound', // Hata sayfası rotası için isim
    component: () => import('pages/ErrorNotFound.vue'),
    meta: {
      title: 'routes.notFound', // i18n anahtarı olarak kullanılacak
      showInNav: false, // Navigasyon menüsünde gösterilmeyecek
      icon: 'error', // Hata sayfası için ikon
      order: 999, // En sonda
    },
  },
];

export default routes;
