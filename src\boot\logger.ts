// src/boot/logger.ts

import { defineBoot } from '#q-app/wrappers';
import { useLogger } from 'src/logging/useLogger';

/**
 * Logger Boot Dosyası
 * 
 * Bu dosya, Quasar uygulaması başlatılırken loglama sistemini yapılandırır ve başlatır.
 * SOLID prensiplerinden Single Responsibility Principle'a uygun olarak,
 * sadece logger başlatma işleminden sorumludur.
 * 
 * Boot dosyası, uygulama başlangıcında çalışır ve:
 * 1. Logger sistemini .env yapılandırmasına göre başlatır
 * 2. Global hata yakalama mekanizmasını kurar
 * 3. Uygulama başlatma bilgilerini loglar
 * 4. Vue instance'ına logger'ı global olarak ekler (opsiyonel)
 */
export default defineBoot(({ app }) => {
    // Logger'ı başlat - bu işlem useLogger() içinde singleton olarak yapılır
    const logger = useLogger();

    // Uygulama başlatma bilgilerini logla
    logger.info('Uygulama başlatılıyor', {
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        environment: import.meta.env.MODE,
    });

    // Global hata yakalama mekanizması kur
    setupGlobalErrorHandling(logger);

    // Vue instance'ına logger'ı global property olarak ekle (opsiyonel)
    // Bu sayede Options API ile yazılmış bileşenlerde this.$logger şeklinde erişilebilir
    app.config.globalProperties.$logger = logger;

    // Development modunda ek bilgilendirme
    if (import.meta.env.DEV) {
        logger.debug('Development modu aktif', {
            logConfig: logger.getConfig(),
            envVars: {
                VITE_LOG_LEVEL: import.meta.env.VITE_LOG_LEVEL,
                VITE_LOG_TO_CONSOLE: import.meta.env.VITE_LOG_TO_CONSOLE,
                VITE_LOG_TO_FILE: import.meta.env.VITE_LOG_TO_FILE,
            },
        });
    }

    // Production modunda performans uyarısı
    if (import.meta.env.PROD && logger.isLevelEnabled(0)) { // DEBUG level
        logger.warn('Production ortamında DEBUG seviyesi aktif', {
            recommendation: 'Performans için log seviyesini INFO veya üzerine çıkarın',
            currentLevel: logger.getConfig().level,
        });
    }
});

/**
 * Global hata yakalama mekanizmasını kurar.
 * Yakalanmamış hatalar ve promise rejection'ları loglar.
 * 
 * @param logger Logger instance'ı
 */
function setupGlobalErrorHandling(logger: ReturnType<typeof useLogger>): void {
    // Yakalanmamış JavaScript hataları
    window.addEventListener('error', (event) => {
        logger.error('Yakalanmamış JavaScript hatası', event.error, {
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            message: event.message,
            timestamp: new Date().toISOString(),
        });
    });

    // Yakalanmamış Promise rejection'ları
    window.addEventListener('unhandledrejection', (event) => {
        logger.error('Yakalanmamış Promise rejection', event.reason, {
            type: 'unhandledrejection',
            timestamp: new Date().toISOString(),
        });
    });

    // Vue hata yakalama (eğer Vue 3 error handler kullanılmıyorsa)
    const originalErrorHandler = window.console.error;
    window.console.error = (...args) => {
        // Vue hatalarını yakala
        if (args[0] && typeof args[0] === 'string' && args[0].includes('[Vue warn]')) {
            logger.warn('Vue uyarısı', undefined, {
                vueWarning: args.join(' '),
                timestamp: new Date().toISOString(),
            });
        }
        
        // Orijinal console.error'ı çağır
        originalErrorHandler.apply(console, args);
    };
}

/**
 * TypeScript için global property tanımı
 * Bu sayede Options API'de this.$logger kullanımı tip güvenli olur
 */
declare module '@vue/runtime-core' {
    interface ComponentCustomProperties {
        $logger: ReturnType<typeof useLogger>;
    }
}
