// src/logging/LoggerFactory.ts

import type { ILogger, LogLevel, LoggerConfig } from './interfaces/ILogger';
import { ConsoleLogger } from './ConsoleLogger';
import { FileLogger } from './FileLogger';

/**
 * @class CompositeLogger
 * Birden fazla logger'ı birleştiren composite pattern uygulaması.
 * SOLID prensiplerinden Open/Closed Principle'a uygun olarak,
 * yeni logger türleri eklenebilir ve mevcut kod değişmez.
 */
class CompositeLogger implements ILogger {
    private loggers: ILogger[] = [];
    private config: LoggerConfig;

    constructor(config: LoggerConfig) {
        this.config = { ...config };
    }

    /**
     * Composite logger'a yeni bir logger ekler.
     * @param logger Eklenecek logger
     */
    addLogger(logger: ILogger): void {
        this.loggers.push(logger);
    }

    /**
     * Debug seviyesinde log mesajı yazar.
     * @param message Log mesajı
     * @param context İsteğe bağlı ek bilgiler
     */
    debug(message: string, context?: Record<string, unknown>): void {
        this.loggers.forEach(logger => logger.debug(message, context));
    }

    /**
     * Info seviyesinde log mesajı yazar.
     * @param message Log mesajı
     * @param context İsteğe bağlı ek bilgiler
     */
    info(message: string, context?: Record<string, unknown>): void {
        this.loggers.forEach(logger => logger.info(message, context));
    }

    /**
     * Warning seviyesinde log mesajı yazar.
     * @param message Log mesajı
     * @param context İsteğe bağlı ek bilgiler
     */
    warn(message: string, context?: Record<string, unknown>): void {
        this.loggers.forEach(logger => logger.warn(message, context));
    }

    /**
     * Error seviyesinde log mesajı yazar.
     * @param message Log mesajı
     * @param error İsteğe bağlı error objesi
     * @param context İsteğe bağlı ek bilgiler
     */
    error(message: string, error?: Error, context?: Record<string, unknown>): void {
        this.loggers.forEach(logger => logger.error(message, error, context));
    }

    /**
     * Fatal seviyesinde log mesajı yazar.
     * @param message Log mesajı
     * @param error İsteğe bağlı error objesi
     * @param context İsteğe bağlı ek bilgiler
     */
    fatal(message: string, error?: Error, context?: Record<string, unknown>): void {
        this.loggers.forEach(logger => logger.fatal(message, error, context));
    }

    /**
     * Belirtilen seviyede log mesajı yazar.
     * @param level Log seviyesi
     * @param message Log mesajı
     * @param error İsteğe bağlı error objesi
     * @param context İsteğe bağlı ek bilgiler
     */
    log(level: LogLevel, message: string, error?: Error, context?: Record<string, unknown>): void {
        this.loggers.forEach(logger => logger.log(level, message, error, context));
    }

    /**
     * Logger'ın mevcut yapılandırmasını döndürür.
     * @returns Logger yapılandırması
     */
    getConfig(): LoggerConfig {
        return { ...this.config };
    }

    /**
     * Belirtilen log seviyesinin aktif olup olmadığını kontrol eder.
     * @param level Kontrol edilecek log seviyesi
     * @returns Seviye aktif ise true, değilse false
     */
    isLevelEnabled(level: LogLevel): boolean {
        return level >= this.config.level;
    }
}

/**
 * @class LoggerFactory
 * Logger oluşturma işlemlerinden sorumlu factory sınıfı.
 * SOLID prensiplerinden Single Responsibility ve Factory Pattern'e uygun olarak,
 * sadece logger oluşturma mantığından sorumludur.
 * 
 * .env dosyasından gelen yapılandırmalara göre uygun logger'ları oluşturur.
 */
export class LoggerFactory {
    /**
     * .env dosyasından logger yapılandırmasını okur ve parse eder.
     * @returns Logger yapılandırması
     * @static
     */
    static createConfigFromEnv(): LoggerConfig {
        // .env'den değerleri al ve parse et
        const logLevel = this.parseLogLevel(import.meta.env.VITE_LOG_LEVEL || 'INFO');
        const toConsole = this.parseBoolean(import.meta.env.VITE_LOG_TO_CONSOLE, true);
        const toFile = this.parseBoolean(import.meta.env.VITE_LOG_TO_FILE, false);
        const filePath = import.meta.env.VITE_LOG_FILE_PATH || 'logs/app.log';
        const maxFileSize = this.parseNumber(import.meta.env.VITE_LOG_MAX_FILE_SIZE, 10485760);
        const dateFormat = import.meta.env.VITE_LOG_DATE_FORMAT || 'YYYY-MM-DD HH:mm:ss';

        return {
            level: logLevel,
            toConsole,
            toFile,
            filePath,
            maxFileSize,
            dateFormat,
        };
    }

    /**
     * Yapılandırmaya göre uygun logger'ı oluşturur.
     * @param config Logger yapılandırması (opsiyonel, verilmezse .env'den alınır)
     * @returns Yapılandırılmış logger instance'ı
     * @static
     */
    static createLogger(config?: LoggerConfig): ILogger {
        const finalConfig = config || this.createConfigFromEnv();

        // Sadece konsol loglama aktifse
        if (finalConfig.toConsole && !finalConfig.toFile) {
            return new ConsoleLogger(finalConfig);
        }

        // Sadece dosya loglama aktifse
        if (!finalConfig.toConsole && finalConfig.toFile) {
            return new FileLogger(finalConfig);
        }

        // Her ikisi de aktifse composite logger kullan
        if (finalConfig.toConsole && finalConfig.toFile) {
            const compositeLogger = new CompositeLogger(finalConfig);
            compositeLogger.addLogger(new ConsoleLogger(finalConfig));
            compositeLogger.addLogger(new FileLogger(finalConfig));
            return compositeLogger;
        }

        // Hiçbiri aktif değilse varsayılan olarak konsol logger döndür
        console.warn('LoggerFactory: Ne konsol ne de dosya loglama aktif değil. Varsayılan konsol logger kullanılıyor.');
        return new ConsoleLogger({ ...finalConfig, toConsole: true });
    }

    /**
     * String log level'ı LogLevel enum'una çevirir.
     * @param levelString String log level
     * @returns LogLevel enum değeri
     * @private
     * @static
     */
    private static parseLogLevel(levelString: string): LogLevel {
        const upperLevel = levelString.toUpperCase();
        switch (upperLevel) {
            case 'DEBUG':
                return 0; // LogLevel.DEBUG
            case 'INFO':
                return 1; // LogLevel.INFO
            case 'WARN':
            case 'WARNING':
                return 2; // LogLevel.WARN
            case 'ERROR':
                return 3; // LogLevel.ERROR
            case 'FATAL':
                return 4; // LogLevel.FATAL
            default:
                console.warn(`LoggerFactory: Bilinmeyen log level '${levelString}'. INFO seviyesi kullanılıyor.`);
                return 1; // LogLevel.INFO
        }
    }

    /**
     * String boolean değerini boolean'a çevirir.
     * @param value String boolean değeri
     * @param defaultValue Varsayılan değer
     * @returns Boolean değer
     * @private
     * @static
     */
    private static parseBoolean(value: string | undefined, defaultValue: boolean): boolean {
        if (value === undefined) {
            return defaultValue;
        }
        return value.toLowerCase() === 'true';
    }

    /**
     * String number değerini number'a çevirir.
     * @param value String number değeri
     * @param defaultValue Varsayılan değer
     * @returns Number değer
     * @private
     * @static
     */
    private static parseNumber(value: string | undefined, defaultValue: number): number {
        if (value === undefined) {
            return defaultValue;
        }
        const parsed = parseInt(value, 10);
        return isNaN(parsed) ? defaultValue : parsed;
    }

    /**
     * Test amaçlı özel yapılandırmalı logger oluşturur.
     * @param level Log seviyesi
     * @param toConsole Konsol loglama aktif mi
     * @param toFile Dosya loglama aktif mi
     * @returns Test logger'ı
     * @static
     */
    static createTestLogger(level: LogLevel = 0, toConsole: boolean = true, toFile: boolean = false): ILogger {
        const testConfig: LoggerConfig = {
            level,
            toConsole,
            toFile,
            filePath: 'logs/test.log',
            maxFileSize: 1048576, // 1MB
            dateFormat: 'YYYY-MM-DD HH:mm:ss',
        };
        return this.createLogger(testConfig);
    }
}
