// src/logging/ConsoleLogger.ts

import type { ILogger, LogLevel, LogEntry, LoggerConfig } from './interfaces/ILogger';

/**
 * @class ConsoleLogger
 * ILogger arayüzünü uygulayan konsol loglama sınıfı.
 * SOLID prensiplerinden Single Responsibility Principle'a uygun olarak,
 * sadece konsol çıktısı ile loglama işleminden sorumludur.
 *
 * Bu sınıf, tarayıcı konsoluna renkli ve formatlanmış log mesajları yazar.
 */
export class ConsoleLogger implements ILogger {
    private config: LoggerConfig;

    /**
     * ConsoleLogger constructor'ı.
     * @param config Logger yapılandırması
     */
    constructor(config: LoggerConfig) {
        this.config = { ...config }; // Immutable config için shallow copy
    }

    /**
     * Debug seviyesinde log mesajı yazar.
     * @param message Log mesajı
     * @param context İsteğe bağlı ek bilgiler
     */
    debug(message: string, context?: Record<string, unknown>): void {
        this.log(0, message, undefined, context); // LogLevel.DEBUG = 0
    }

    /**
     * Info seviyesinde log mesajı yazar.
     * @param message Log mesajı
     * @param context İsteğe bağlı ek bilgiler
     */
    info(message: string, context?: Record<string, unknown>): void {
        this.log(1, message, undefined, context); // LogLevel.INFO = 1
    }

    /**
     * Warning seviyesinde log mesajı yazar.
     * @param message Log mesajı
     * @param context İsteğe bağlı ek bilgiler
     */
    warn(message: string, context?: Record<string, unknown>): void {
        this.log(2, message, undefined, context); // LogLevel.WARN = 2
    }

    /**
     * Error seviyesinde log mesajı yazar.
     * @param message Log mesajı
     * @param error İsteğe bağlı error objesi
     * @param context İsteğe bağlı ek bilgiler
     */
    error(message: string, error?: Error, context?: Record<string, unknown>): void {
        this.log(3, message, error, context); // LogLevel.ERROR = 3
    }

    /**
     * Fatal seviyesinde log mesajı yazar.
     * @param message Log mesajı
     * @param error İsteğe bağlı error objesi
     * @param context İsteğe bağlı ek bilgiler
     */
    fatal(message: string, error?: Error, context?: Record<string, unknown>): void {
        this.log(4, message, error, context); // LogLevel.FATAL = 4
    }

    /**
     * Belirtilen seviyede log mesajı yazar.
     * Ana loglama metodu - tüm diğer metotlar bu metodu kullanır.
     * @param level Log seviyesi
     * @param message Log mesajı
     * @param error İsteğe bağlı error objesi
     * @param context İsteğe bağlı ek bilgiler
     */
    log(level: LogLevel, message: string, error?: Error, context?: Record<string, unknown>): void {
        // Seviye kontrolü - performans optimizasyonu
        if (!this.isLevelEnabled(level)) {
            return;
        }

        // Log entry oluştur
        const logEntry: LogEntry = {
            level,
            message,
            timestamp: new Date(),
            ...(context && { context }),
            ...(error && { error }),
        };

        // Konsola formatlanmış mesaj yaz
        this.writeToConsole(logEntry);
    }

    /**
     * Logger'ın mevcut yapılandırmasını döndürür.
     * @returns Logger yapılandırması
     */
    getConfig(): LoggerConfig {
        return { ...this.config }; // Immutable return için shallow copy
    }

    /**
     * Belirtilen log seviyesinin aktif olup olmadığını kontrol eder.
     * @param level Kontrol edilecek log seviyesi
     * @returns Seviye aktif ise true, değilse false
     */
    isLevelEnabled(level: LogLevel): boolean {
        return level >= this.config.level;
    }

    /**
     * Log entry'yi konsola formatlanmış şekilde yazar.
     * Her log seviyesi için farklı renk ve stil kullanır.
     * @param logEntry Yazılacak log entry
     * @private
     */
    private writeToConsole(logEntry: LogEntry): void {
        const timestamp = this.formatTimestamp(logEntry.timestamp);
        const levelName = this.getLevelName(logEntry.level);
        const formattedMessage = `[${timestamp}] ${levelName}: ${logEntry.message}`;

        // Log seviyesine göre uygun konsol metodunu seç
        const levelValue = logEntry.level as number;
        switch (levelValue) {
            case 0: // DEBUG
                console.debug(
                    `%c${formattedMessage}`,
                    'color: #6B7280; font-weight: normal;',
                    ...(logEntry.context ? [logEntry.context] : [])
                );
                break;
            case 1: // INFO
                console.info(
                    `%c${formattedMessage}`,
                    'color:rgb(196, 219, 255); font-weight: normal;',
                    ...(logEntry.context ? [logEntry.context] : [])
                );
                break;
            case 2: // WARN
                console.warn(
                    `%c${formattedMessage}`,
                    'color: #F59E0B; font-weight: bold;',
                    ...(logEntry.context ? [logEntry.context] : [])
                );
                break;
            case 3: // ERROR
                console.error(
                    `%c${formattedMessage}`,
                    'color: #EF4444; font-weight: bold;',
                    ...(logEntry.error ? [logEntry.error] : []),
                    ...(logEntry.context ? [logEntry.context] : [])
                );
                break;
            case 4: // FATAL
                console.error(
                    `%c${formattedMessage}`,
                    'color: #DC2626; font-weight: bold; background-color: #FEE2E2; padding: 2px 4px;',
                    ...(logEntry.error ? [logEntry.error] : []),
                    ...(logEntry.context ? [logEntry.context] : [])
                );
                break;
            default:
                console.log(formattedMessage, ...(logEntry.context ? [logEntry.context] : []));
        }
    }

    /**
     * Timestamp'i belirtilen formatta string'e çevirir.
     * @param timestamp Formatlanacak timestamp
     * @returns Formatlanmış timestamp string'i
     * @private
     */
    private formatTimestamp(timestamp: Date): string {
        // Basit ISO format kullan - gelecekte moment.js veya date-fns eklenebilir
        return timestamp.toISOString().replace('T', ' ').substring(0, 19);
    }

    /**
     * Log seviyesinin string karşılığını döndürür.
     * @param level Log seviyesi
     * @returns Seviye adı
     * @private
     */
    private getLevelName(level: LogLevel): string {
        const levelNames = ['DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL'];
        return levelNames[level] || 'UNKNOWN';
    }
}
