// src/logging/interfaces/ILogger.ts

/**
 * @enum LogLevel
 * Loglama seviyelerini tanımlar.
 * Her seviye, kendisinden düşük seviyeleri de içerir.
 * Örneğin: INFO seviyesi DEBUG mesajlarını göstermez, ancak WARN ve ERROR mesajlarını gösterir.
 */
export enum LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3,
    FATAL = 4,
}

/**
 * @interface LogEntry
 * Bir log girişinin yapısını tanımlar.
 * Tüm log mesajları bu yapıya uygun olarak oluşturulur.
 */
export interface LogEntry {
    level: LogLevel;
    message: string;
    timestamp: Date;
    context?: Record<string, unknown>; // İsteğe bağlı ek bilgiler
    error?: Error; // Hata durumunda error objesi
}

/**
 * @interface LoggerConfig
 * Logger yapılandırma seçeneklerini tanımlar.
 * .env dosyasından gelen değerler bu interface'e uygun olarak parse edilir.
 */
export interface LoggerConfig {
    level: LogLevel;
    toConsole: boolean;
    toFile: boolean;
    filePath?: string;
    maxFileSize?: number;
    dateFormat?: string;
}

/**
 * @interface ILogger
 * Logger servisi için temel arayüz.
 * SOLID prensiplerinden Interface Segregation Principle'a uygun olarak,
 * sadece loglama ile ilgili temel metotları tanımlar.
 * 
 * Bu arayüz, somut logger uygulamalarının (ConsoleLogger, FileLogger vb.) 
 * uyması gereken sözleşmedir.
 */
export interface ILogger {
    /**
     * Debug seviyesinde log mesajı yazar.
     * Geliştirme aşamasında detaylı bilgiler için kullanılır.
     * @param message Log mesajı
     * @param context İsteğe bağlı ek bilgiler
     */
    debug(message: string, context?: Record<string, unknown>): void;

    /**
     * Info seviyesinde log mesajı yazar.
     * Genel bilgilendirme mesajları için kullanılır.
     * @param message Log mesajı
     * @param context İsteğe bağlı ek bilgiler
     */
    info(message: string, context?: Record<string, unknown>): void;

    /**
     * Warning seviyesinde log mesajı yazar.
     * Uyarı mesajları için kullanılır.
     * @param message Log mesajı
     * @param context İsteğe bağlı ek bilgiler
     */
    warn(message: string, context?: Record<string, unknown>): void;

    /**
     * Error seviyesinde log mesajı yazar.
     * Hata mesajları için kullanılır.
     * @param message Log mesajı
     * @param error İsteğe bağlı error objesi
     * @param context İsteğe bağlı ek bilgiler
     */
    error(message: string, error?: Error, context?: Record<string, unknown>): void;

    /**
     * Fatal seviyesinde log mesajı yazar.
     * Kritik hatalar için kullanılır.
     * @param message Log mesajı
     * @param error İsteğe bağlı error objesi
     * @param context İsteğe bağlı ek bilgiler
     */
    fatal(message: string, error?: Error, context?: Record<string, unknown>): void;

    /**
     * Belirtilen seviyede log mesajı yazar.
     * Genel amaçlı loglama metodu.
     * @param level Log seviyesi
     * @param message Log mesajı
     * @param error İsteğe bağlı error objesi
     * @param context İsteğe bağlı ek bilgiler
     */
    log(level: LogLevel, message: string, error?: Error, context?: Record<string, unknown>): void;

    /**
     * Logger'ın mevcut yapılandırmasını döndürür.
     * @returns Logger yapılandırması
     */
    getConfig(): LoggerConfig;

    /**
     * Belirtilen log seviyesinin aktif olup olmadığını kontrol eder.
     * Performans optimizasyonu için kullanılır.
     * @param level Kontrol edilecek log seviyesi
     * @returns Seviye aktif ise true, değilse false
     */
    isLevelEnabled(level: LogLevel): boolean;
}
