// src/logging/FileLogger.ts

import type { ILogger, LogLevel, LogEntry, LoggerConfig } from './interfaces/ILogger';

/**
 * @class FileLogger
 * ILogger arayüzünü uygulayan dosya loglama sınıfı.
 * SOLID prensiplerinden Single Responsibility Principle'a uygun olarak,
 * sadece dosya çıktısı ile loglama işleminden sorumludur.
 *
 * Bu sınıf, log mesajlarını belirtilen dosyaya yazar.
 * Tarayıcı ortamında File System API kullanımı sınırlı olduğu için,
 * bu implementasyon localStorage kullanarak log'ları saklar.
 */
export class FileLogger implements ILogger {
    private config: LoggerConfig;
    private readonly storageKey: string;

    /**
     * FileLogger constructor'ı.
     * @param config Logger yapılandırması
     */
    constructor(config: LoggerConfig) {
        this.config = { ...config }; // Immutable config için shallow copy
        this.storageKey = `app_logs_${this.config.filePath?.replace(/[^a-zA-Z0-9]/g, '_') || 'default'}`;
    }

    /**
     * Debug seviyesinde log mesajı yazar.
     * @param message Log mesajı
     * @param context İsteğe bağlı ek bilgiler
     */
    debug(message: string, context?: Record<string, unknown>): void {
        this.log(0, message, undefined, context); // LogLevel.DEBUG = 0
    }

    /**
     * Info seviyesinde log mesajı yazar.
     * @param message Log mesajı
     * @param context İsteğe bağlı ek bilgiler
     */
    info(message: string, context?: Record<string, unknown>): void {
        this.log(1, message, undefined, context); // LogLevel.INFO = 1
    }

    /**
     * Warning seviyesinde log mesajı yazar.
     * @param message Log mesajı
     * @param context İsteğe bağlı ek bilgiler
     */
    warn(message: string, context?: Record<string, unknown>): void {
        this.log(2, message, undefined, context); // LogLevel.WARN = 2
    }

    /**
     * Error seviyesinde log mesajı yazar.
     * @param message Log mesajı
     * @param error İsteğe bağlı error objesi
     * @param context İsteğe bağlı ek bilgiler
     */
    error(message: string, error?: Error, context?: Record<string, unknown>): void {
        this.log(3, message, error, context); // LogLevel.ERROR = 3
    }

    /**
     * Fatal seviyesinde log mesajı yazar.
     * @param message Log mesajı
     * @param error İsteğe bağlı error objesi
     * @param context İsteğe bağlı ek bilgiler
     */
    fatal(message: string, error?: Error, context?: Record<string, unknown>): void {
        this.log(4, message, error, context); // LogLevel.FATAL = 4
    }

    /**
     * Belirtilen seviyede log mesajı yazar.
     * Ana loglama metodu - tüm diğer metotlar bu metodu kullanır.
     * @param level Log seviyesi
     * @param message Log mesajı
     * @param error İsteğe bağlı error objesi
     * @param context İsteğe bağlı ek bilgiler
     */
    log(level: LogLevel, message: string, error?: Error, context?: Record<string, unknown>): void {
        // Seviye kontrolü - performans optimizasyonu
        if (!this.isLevelEnabled(level)) {
            return;
        }

        // Log entry oluştur
        const logEntry: LogEntry = {
            level,
            message,
            timestamp: new Date(),
            ...(context && { context }),
            ...(error && { error }),
        };

        // Dosyaya formatlanmış mesaj yaz
        this.writeToFile(logEntry);
    }

    /**
     * Logger'ın mevcut yapılandırmasını döndürür.
     * @returns Logger yapılandırması
     */
    getConfig(): LoggerConfig {
        return { ...this.config }; // Immutable return için shallow copy
    }

    /**
     * Belirtilen log seviyesinin aktif olup olmadığını kontrol eder.
     * @param level Kontrol edilecek log seviyesi
     * @returns Seviye aktif ise true, değilse false
     */
    isLevelEnabled(level: LogLevel): boolean {
        return level >= this.config.level;
    }

    /**
     * Log entry'yi dosyaya (localStorage) formatlanmış şekilde yazar.
     * @param logEntry Yazılacak log entry
     * @private
     */
    private writeToFile(logEntry: LogEntry): void {
        try {
            // Debug: Storage key'i konsola yazdır
            console.debug(`FileLogger: Writing to storage key: ${this.storageKey}`);

            // Mevcut log'ları al
            const existingLogs = this.getExistingLogs();

            // Yeni log entry'yi formatla
            const formattedEntry = this.formatLogEntry(logEntry);

            // Yeni log'u ekle
            existingLogs.push(formattedEntry);

            // Dosya boyutu kontrolü yap
            const logsToSave = this.enforceMaxFileSize(existingLogs);

            // localStorage'a kaydet
            localStorage.setItem(this.storageKey, JSON.stringify(logsToSave));

            // Debug: Kaydedilen log sayısını yazdır
            console.debug(`FileLogger: Saved ${logsToSave.length} logs to localStorage`);

        } catch (error) {
            // Dosya yazma hatası durumunda konsola yaz
            console.error('FileLogger: Log yazma hatası:', error);
        }
    }

    /**
     * Mevcut log'ları localStorage'dan alır.
     * @returns Mevcut log array'i
     * @private
     */
    private getExistingLogs(): string[] {
        try {
            const stored = localStorage.getItem(this.storageKey);
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.error('FileLogger: Log okuma hatası:', error);
            return [];
        }
    }

    /**
     * Log entry'yi string formatına çevirir.
     * @param logEntry Formatlanacak log entry
     * @returns Formatlanmış log string'i
     * @private
     */
    private formatLogEntry(logEntry: LogEntry): string {
        const timestamp = this.formatTimestamp(logEntry.timestamp);
        const levelName = this.getLevelName(logEntry.level);

        let formattedEntry = `[${timestamp}] ${levelName}: ${logEntry.message}`;

        // Error bilgisi varsa ekle
        if (logEntry.error) {
            formattedEntry += ` | Error: ${logEntry.error.message}`;
            if (logEntry.error.stack) {
                formattedEntry += ` | Stack: ${logEntry.error.stack}`;
            }
        }

        // Context bilgisi varsa ekle
        if (logEntry.context && Object.keys(logEntry.context).length > 0) {
            formattedEntry += ` | Context: ${JSON.stringify(logEntry.context)}`;
        }

        return formattedEntry;
    }

    /**
     * Maksimum dosya boyutunu kontrol eder ve gerekirse eski log'ları siler.
     * @param logs Mevcut log array'i
     * @returns Boyut sınırına uygun log array'i
     * @private
     */
    private enforceMaxFileSize(logs: string[]): string[] {
        const maxSize = this.config.maxFileSize || 10485760; // 10MB default
        const currentSize = JSON.stringify(logs).length;

        if (currentSize <= maxSize) {
            return logs;
        }

        // Boyut aşıldıysa, eski log'ları sil (FIFO)
        const targetSize = maxSize * 0.8; // %80'ine düşür
        const reducedLogs = [...logs];
        let reducedSize = currentSize;

        while (reducedSize > targetSize && reducedLogs.length > 1) {
            reducedLogs.shift(); // İlk elemanı sil
            reducedSize = JSON.stringify(reducedLogs).length;
        }

        return reducedLogs;
    }

    /**
     * Timestamp'i belirtilen formatta string'e çevirir.
     * @param timestamp Formatlanacak timestamp
     * @returns Formatlanmış timestamp string'i
     * @private
     */
    private formatTimestamp(timestamp: Date): string {
        // Basit ISO format kullan - gelecekte moment.js veya date-fns eklenebilir
        return timestamp.toISOString().replace('T', ' ').substring(0, 19);
    }

    /**
     * Log seviyesinin string karşılığını döndürür.
     * @param level Log seviyesi
     * @returns Seviye adı
     * @private
     */
    private getLevelName(level: LogLevel): string {
        const levelNames = ['DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL'];
        return levelNames[level] || 'UNKNOWN';
    }

    /**
     * Tüm log'ları temizler.
     * Geliştirme ve test amaçlı kullanılır.
     */
    clearLogs(): void {
        try {
            localStorage.removeItem(this.storageKey);
        } catch (error) {
            console.error('FileLogger: Log temizleme hatası:', error);
        }
    }

    /**
     * Mevcut tüm log'ları döndürür.
     * Debug ve analiz amaçlı kullanılır.
     * @returns Tüm log'ların array'i
     */
    getAllLogs(): string[] {
        return this.getExistingLogs();
    }
}
